<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;68c1e013-5aab-4257-b824-7cc26cffc5b1&quot;,&quot;conversations&quot;:{&quot;b01f3134-46db-4934-8f66-210145cf60fe&quot;:{&quot;id&quot;:&quot;b01f3134-46db-4934-8f66-210145cf60fe&quot;,&quot;createdAtIso&quot;:&quot;2025-07-30T05:32:19.956Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-30T05:32:19.956Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;349b5e87-e0a5-44ae-bd82-5d7896d26748&quot;},&quot;68c1e013-5aab-4257-b824-7cc26cffc5b1&quot;:{&quot;id&quot;:&quot;68c1e013-5aab-4257-b824-7cc26cffc5b1&quot;,&quot;createdAtIso&quot;:&quot;2025-07-30T05:32:20.003Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-30T05:32:38.505Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;151fbcb1-e4e6-482a-8b3a-210ece9e37bf&quot;,&quot;request_message&quot;:&quot;推送到空仓库git\nhttps://gitee.com/xpeng888/mail-data-management-system.git&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;推送到空仓库git&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;https://gitee.com/xpeng888/mail-data-management-system.git&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;推送到空仓库git\nhttps://gitee.com/xpeng888/mail-data-management-system.git&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;D:/PythonProject/mail数据管理系统&quot;,&quot;folderRoot&quot;:&quot;D:/PythonProject/mail数据管理系统&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T05:32:38.517Z&quot;,&quot;response_text&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-15fdb23a-6d62-4044-956a-60fa4c178b13&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;af99d17e-17f6-4dd6-a800-4a1eed4fa9f6&quot;}},&quot;agentExecutionMode&quot;:&quot;auto&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>